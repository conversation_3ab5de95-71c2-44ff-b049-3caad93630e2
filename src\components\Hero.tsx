
import { But<PERSON> } from "@/components/ui/button";
import { ArrowR<PERSON> } from "lucide-react";

const Hero = () => {
  return (
    <div className="min-h-screen flex flex-col bg-background">
      {/* Header */}
      <header className="flex items-center justify-between p-8 md:p-12">
        <div className="text-xl font-semibold text-neutral-900">
          Svensk Böranalys
        </div>
        <div className="hidden md:block text-sm text-neutral-500">
          Professionell aktieanalys
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 flex items-center justify-center px-8 md:px-12">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-hero-sm md:text-hero font-bold text-neutral-900 mb-8 animate-fade-in">
            Analysera svenska
            <br />
            <span className="text-gradient">börsbolag</span>
          </h1>
          
          <p className="text-lg md:text-xl text-neutral-600 mb-12 max-w-2xl mx-auto leading-relaxed animate-slide-up">
            Få djupgående analyser, nyckeltal och insikter om svenska aktier. 
            Fatta bättre investeringsbeslut med våra avancerade analysverktyg.
          </p>
          
          <div className="animate-slide-up">
            <Button 
              size="lg" 
              className="bg-accent-blue hover:bg-accent-blue/90 text-white font-medium px-8 py-4 text-lg rounded-lg transition-all duration-200 hover:scale-105"
            >
              Kom igång gratis
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </div>
          
          <div className="mt-16 text-sm text-neutral-400">
            Ingen bindningstid • Gratis i 14 dagar
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="p-8 md:p-12 text-center text-sm text-neutral-400">
        © 2024 Svensk Böranalys. Alla rättigheter förbehållna.
      </footer>
    </div>
  );
};

export default Hero;
