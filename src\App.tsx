import { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';
import { CompanyDashboard } from './components/CompanyDashboard';
import { CompanyForm } from './components/CompanyForm';
import { CompanyDetails } from './components/CompanyDetails';
import { Toaster } from 'sonner';

function App() {
  return (
    <Router>
      <div className="min-h-screen bg-gray-50">
        <header className="bg-white shadow">
          <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center">
              <h1 className="text-3xl font-bold text-gray-900">
                Corporate Finance Opportunity Analyzer
              </h1>
              <nav className="space-x-4">
                <Link
                  to="/"
                  className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
                >
                  Dashboard
                </Link>
                <Link
                  to="/add-company"
                  className="bg-blue-600 text-white hover:bg-blue-700 px-4 py-2 rounded-md text-sm font-medium"
                >
                  Lägg till företag
                </Link>
              </nav>
            </div>
          </div>
        </header>

        <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            <Routes>
              <Route path="/" element={<CompanyDashboard />} />
              <Route path="/add-company" element={<CompanyForm />} />
              <Route path="/edit-company/:id" element={<CompanyForm />} />
              <Route path="/company/:id" element={<CompanyDetails />} />
            </Routes>
          </div>
        </main>

        <Toaster position="top-right" />
      </div>
    </Router>
  );
}

export default App;
