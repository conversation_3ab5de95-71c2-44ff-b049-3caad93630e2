import { RiskAnalysis as RiskAnalysisType } from '../lib/supabase';

interface RiskAnalysisProps {
  analysis: RiskAnalysisType;
}

const RISK_CATEGORIES = {
  LOW: { label: 'Låg risk', color: 'bg-green-100 text-green-800' },
  MEDIUM: { label: 'Medelhög risk', color: 'bg-yellow-100 text-yellow-800' },
  HIGH: { label: 'Hög risk', color: 'bg-red-100 text-red-800' },
} as const;

export function RiskAnalysis({ analysis }: RiskAnalysisProps) {
  const getRiskCategory = (score: number) => {
    if (score < 40) return RISK_CATEGORIES.LOW;
    if (score < 70) return RISK_CATEGORIES.MEDIUM;
    return RISK_CATEGORIES.HIGH;
  };

  const totalRiskCategory = getRiskCategory(analysis.total_risk_score);

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h2 className="text-2xl font-bold mb-4">Riskanalys</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Risk Scores */}
        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-semibold mb-2">Likviditetsrisk</h3>
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div
                className="bg-blue-600 h-2.5 rounded-full"
                style={{ width: `${analysis.liquidity_risk}%` }}
              ></div>
            </div>
            <p className="text-sm text-gray-600 mt-1">{analysis.liquidity_risk}%</p>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-2">Skuldsättningsrisk</h3>
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div
                className="bg-blue-600 h-2.5 rounded-full"
                style={{ width: `${analysis.leverage_risk}%` }}
              ></div>
            </div>
            <p className="text-sm text-gray-600 mt-1">{analysis.leverage_risk}%</p>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-2">Lönsamhetsrisk</h3>
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div
                className="bg-blue-600 h-2.5 rounded-full"
                style={{ width: `${analysis.profitability_risk}%` }}
              ></div>
            </div>
            <p className="text-sm text-gray-600 mt-1">{analysis.profitability_risk}%</p>
          </div>
        </div>

        {/* Total Risk and Recommendations */}
        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-semibold mb-2">Total Risk</h3>
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div
                className={`h-2.5 rounded-full ${
                  totalRiskCategory === RISK_CATEGORIES.LOW
                    ? 'bg-green-600'
                    : totalRiskCategory === RISK_CATEGORIES.MEDIUM
                    ? 'bg-yellow-600'
                    : 'bg-red-600'
                }`}
                style={{ width: `${analysis.total_risk_score}%` }}
              ></div>
            </div>
            <div className="flex items-center gap-2 mt-1">
              <span className={`px-2 py-1 rounded-full text-sm ${totalRiskCategory.color}`}>
                {totalRiskCategory.label}
              </span>
              <span className="text-sm text-gray-600">{analysis.total_risk_score}%</span>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-2">Rekommendationer</h3>
            <ul className="list-disc list-inside space-y-1">
              {analysis.recommendations.map((recommendation, index) => (
                <li key={index} className="text-gray-700">
                  {recommendation}
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
} 