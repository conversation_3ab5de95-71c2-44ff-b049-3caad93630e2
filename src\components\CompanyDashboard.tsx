import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { supabase, TABLES, Company, SECTORS, STOCK_EXCHANGES } from '../lib/supabase';
import { toast } from 'sonner';
import { Button } from './ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Eye, Plus, Building2 } from 'lucide-react';

type SortField = 'name' | 'ticker' | 'sector' | 'listing_exchange' | 'created_at';
type SortOrder = 'asc' | 'desc';

export function CompanyDashboard() {
  const [companies, setCompanies] = useState<Company[]>([]);
  const [loading, setLoading] = useState(true);
  const [sortField, setSortField] = useState<SortField>('created_at');
  const [sortOrder, setSortOrder] = useState<SortOrder>('desc');
  const [filterSector, setFilterSector] = useState<string>('');
  const [filterExchange, setFilterExchange] = useState<string>('');

  useEffect(() => {
    fetchCompanies();
  }, [sortField, sortOrder, filterSector, filterExchange]);

  const fetchCompanies = async () => {
    try {
      setLoading(true);
      let query = supabase
        .from(TABLES.COMPANIES)
        .select('*');

      if (filterSector) {
        query = query.eq('sector', filterSector);
      }

      if (filterExchange) {
        query = query.eq('listing_exchange', filterExchange);
      }

      const { data, error } = await query
        .order(sortField, { ascending: sortOrder === 'asc' });

      if (error) throw error;
      setCompanies(data || []);
    } catch (error) {
      console.error('Error fetching companies:', error);
      toast.error('Kunde inte hämta företag');
    } finally {
      setLoading(false);
    }
  };

  const handleSort = (field: SortField) => {
    if (field === sortField) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortOrder('desc');
    }
  };

  const getSectorDisplayName = (sectorKey: string) => {
    return SECTORS[sectorKey as keyof typeof SECTORS] || sectorKey;
  };

  const getExchangeDisplayName = (exchangeKey: string) => {
    return STOCK_EXCHANGES[exchangeKey as keyof typeof STOCK_EXCHANGES] || exchangeKey;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Företagsportfölj</h1>
          <p className="text-gray-600">Hantera och analysera svenska småbolag</p>
        </div>
        <Link to="/add-company">
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Lägg till företag
          </Button>
        </Link>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Filter</CardTitle>
          <CardDescription>Filtrera företag efter sektor och börs</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Sektor</label>
              <Select value={filterSector} onValueChange={setFilterSector}>
                <SelectTrigger>
                  <SelectValue placeholder="Alla sektorer" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Alla sektorer</SelectItem>
                  {Object.entries(SECTORS).map(([key, value]) => (
                    <SelectItem key={key} value={key}>
                      {value}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Börs</label>
              <Select value={filterExchange} onValueChange={setFilterExchange}>
                <SelectTrigger>
                  <SelectValue placeholder="Alla börser" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Alla börser</SelectItem>
                  {Object.entries(STOCK_EXCHANGES).map(([key, value]) => (
                    <SelectItem key={key} value={key}>
                      {value}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Sortera efter</label>
              <Select value={sortField} onValueChange={(value) => setSortField(value as SortField)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="name">Företagsnamn</SelectItem>
                  <SelectItem value="ticker">Ticker</SelectItem>
                  <SelectItem value="sector">Sektor</SelectItem>
                  <SelectItem value="listing_exchange">Börs</SelectItem>
                  <SelectItem value="created_at">Skapad</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Companies Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {loading ? (
          Array.from({ length: 6 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                </div>
              </CardContent>
            </Card>
          ))
        ) : companies.length === 0 ? (
          <div className="col-span-full">
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <Building2 className="h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Inga företag hittades</h3>
                <p className="text-gray-500 text-center mb-4">
                  {filterSector || filterExchange
                    ? 'Inga företag matchar dina filterkriterier. Prova att justera filtren.'
                    : 'Du har inte lagt till några företag än. Kom igång genom att lägga till ditt första företag.'
                  }
                </p>
                <Link to="/add-company">
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Lägg till första företaget
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        ) : (
          companies.map((company) => (
            <Card key={company.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-lg">{company.name}</CardTitle>
                    <CardDescription className="font-mono text-sm">
                      {company.ticker}
                    </CardDescription>
                  </div>
                  <Link to={`/company/${company.id}`}>
                    <Button variant="outline" size="sm">
                      <Eye className="h-4 w-4" />
                    </Button>
                  </Link>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-500">Sektor:</span>
                    <Badge variant="secondary">
                      {getSectorDisplayName(company.sector)}
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-500">Börs:</span>
                    <Badge variant="outline">
                      {getExchangeDisplayName(company.listing_exchange)}
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-500">Noterad:</span>
                    <span className="text-sm">
                      {new Date(company.listing_date).toLocaleDateString('sv-SE')}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-500">Tillagd:</span>
                    <span className="text-sm">
                      {new Date(company.created_at).toLocaleDateString('sv-SE')}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
}