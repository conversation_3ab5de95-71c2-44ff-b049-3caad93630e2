/**
 * Database Migration Script for Corporate Finance Analyzer
 * This script applies the comprehensive schema migration to Supabase
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';

// Supabase configuration
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://zxaxmmrlfwnvstthdbqf.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY; // Service key needed for admin operations

if (!supabaseServiceKey) {
  console.error('SUPABASE_SERVICE_KEY environment variable is required for migration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function runMigration() {
  try {
    console.log('🚀 Starting database migration...');
    
    // Read the migration SQL file
    const migrationPath = path.join(process.cwd(), 'supabase', 'migrations', '001_comprehensive_schema.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    console.log('📄 Migration SQL loaded successfully');
    
    // Split the SQL into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    console.log(`📝 Found ${statements.length} SQL statements to execute`);
    
    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      console.log(`⚡ Executing statement ${i + 1}/${statements.length}...`);
      
      try {
        const { error } = await supabase.rpc('exec_sql', { sql: statement });
        if (error) {
          console.error(`❌ Error in statement ${i + 1}:`, error);
          // Continue with other statements for non-critical errors
        } else {
          console.log(`✅ Statement ${i + 1} executed successfully`);
        }
      } catch (err) {
        console.error(`❌ Exception in statement ${i + 1}:`, err);
      }
    }
    
    console.log('🎉 Migration completed!');
    
    // Verify the new tables exist
    console.log('🔍 Verifying migration...');
    const { data: tables, error: tablesError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .in('table_name', ['companies', 'company_documents', 'financial_data', 'service_opportunities']);
    
    if (tablesError) {
      console.error('❌ Error verifying tables:', tablesError);
    } else {
      console.log('✅ Tables verified:', tables.map(t => t.table_name));
    }
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Alternative approach: Manual table creation
async function createTablesManually() {
  console.log('🔧 Creating tables manually...');
  
  try {
    // Create companies table
    const { error: companiesError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS companies (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          name TEXT NOT NULL,
          ticker TEXT UNIQUE NOT NULL,
          sector TEXT NOT NULL,
          listing_exchange TEXT NOT NULL,
          listing_date DATE NOT NULL,
          created_at TIMESTAMP DEFAULT NOW(),
          updated_at TIMESTAMP DEFAULT NOW()
        );
      `
    });
    
    if (companiesError) {
      console.error('❌ Error creating companies table:', companiesError);
    } else {
      console.log('✅ Companies table created');
    }
    
    // Add more table creation statements here...
    
  } catch (error) {
    console.error('❌ Manual table creation failed:', error);
  }
}

// Run the migration
if (process.argv.includes('--manual')) {
  createTablesManually();
} else {
  runMigration();
}
