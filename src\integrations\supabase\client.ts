// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://zxaxmmrlfwnvstthdbqf.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp4YXhtbXJsZndudnN0dGhkYnFmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk0ODU3MjUsImV4cCI6MjA2NTA2MTcyNX0.G2JFEEg0K9vtnFUDa17zVdy71r9C7_Ky8f5vzm-YyyU";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);