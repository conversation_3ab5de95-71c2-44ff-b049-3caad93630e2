import { useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { supabase, STORAGE_BUCKETS, TABLES } from '../lib/supabase';
import { processExcelFile } from '../lib/excelProcessor';
import { toast } from 'sonner';

interface QuarterlyReportUploadProps {
  companyId: string;
  onSuccess?: () => void;
}

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

export function QuarterlyReportUpload({ companyId, onSuccess }: QuarterlyReportUploadProps) {
  const [isUploading, setIsUploading] = useState(false);

  const onDrop = async (acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    
    if (!file) return;
    
    // Validate file size
    if (file.size > MAX_FILE_SIZE) {
      toast.error('Filen är för stor (max 10MB)');
      return;
    }

    // Validate file type
    if (!file.name.match(/\.(xlsx|xls)$/)) {
      toast.error('Endast Excel-filer (.xlsx, .xls) är tillåtna');
      return;
    }

    try {
      setIsUploading(true);
      
      // Process Excel file
      const { quarterlyReport, serviceOpportunityScore } = await processExcelFile(file, companyId);

      // Upload file to Supabase Storage
      const fileName = `quarterly-reports/${companyId}/${Date.now()}_${file.name}`;
      const { error: uploadError } = await supabase.storage
        .from(STORAGE_BUCKETS.QUARTERLY_REPORTS)
        .upload(fileName, file, {
          cacheControl: '3600',
          contentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        });

      if (uploadError) throw uploadError;

      // Save quarterly report to database
      const { error: reportError } = await supabase
        .from(TABLES.QUARTERLY_REPORTS)
        .insert([quarterlyReport]);

      if (reportError) throw reportError;

      // Update company's service opportunity score
      const { error: updateError } = await supabase
        .from(TABLES.COMPANIES)
        .update({
          service_opportunity_score: serviceOpportunityScore,
          last_updated: new Date().toISOString()
        })
        .eq('id', companyId);

      if (updateError) throw updateError;

      toast.success('Kvartalsrapport uppladdad framgångsrikt');
      onSuccess?.();
      
    } catch (error) {
      console.error('Upload error:', error);
      toast.error('Ett fel uppstod vid uppladdning');
    } finally {
      setIsUploading(false);
    }
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'application/vnd.ms-excel': ['.xls']
    },
    maxFiles: 1
  });

  return (
    <div
      {...getRootProps()}
      className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
        ${isDragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'}`}
    >
      <input {...getInputProps()} />
      {isUploading ? (
        <div className="flex flex-col items-center gap-2">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          <p>Laddar upp fil...</p>
        </div>
      ) : (
        <div className="flex flex-col items-center gap-2">
          <svg
            className="w-12 h-12 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
            />
          </svg>
          <p className="text-lg font-medium">
            {isDragActive
              ? 'Släpp filen här...'
              : 'Dra och släpp en Excel-fil här, eller klicka för att välja'}
          </p>
          <p className="text-sm text-gray-500">
            Endast .xlsx och .xls filer, max 10MB
          </p>
        </div>
      )}
    </div>
  );
} 