import { useState, useEffect } from 'react';
import { use<PERSON>arams, Link } from 'react-router-dom';
import { supabase, TABLES, Company, CompanyDocument, FinancialData, SECTORS, STOCK_EXCHANGES } from '../lib/supabase';
import { toast } from 'sonner';
import { Button } from './ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from './ui/dialog';
import { ArrowLeft, Upload, FileText, TrendingUp, AlertTriangle } from 'lucide-react';
import { DocumentUpload } from './DocumentUpload';

export function CompanyDetails() {
  const { id } = useParams<{ id: string }>();
  const [company, setCompany] = useState<Company | null>(null);
  const [documents, setDocuments] = useState<CompanyDocument[]>([]);
  const [financialData, setFinancialData] = useState<FinancialData[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [showUploadDialog, setShowUploadDialog] = useState(false);

  useEffect(() => {
    if (id) {
      fetchCompanyDetails();
    }
  }, [id]);

  const fetchCompanyDetails = async () => {
    try {
      setLoading(true);

      // Fetch company details
      const { data: companyData, error: companyError } = await supabase
        .from(TABLES.COMPANIES)
        .select('*')
        .eq('id', id)
        .single();

      if (companyError) throw companyError;
      setCompany(companyData);

      // Fetch company documents
      const { data: documentsData, error: documentsError } = await supabase
        .from(TABLES.COMPANY_DOCUMENTS)
        .select('*')
        .eq('company_id', id)
        .order('uploaded_at', { ascending: false });

      if (documentsError) throw documentsError;
      setDocuments(documentsData || []);

      // Fetch financial data
      const { data: financialDataResult, error: financialError } = await supabase
        .from(TABLES.FINANCIAL_DATA)
        .select('*')
        .eq('company_id', id)
        .order('period', { ascending: false });

      if (financialError) throw financialError;
      setFinancialData(financialDataResult || []);
    } catch (error) {
      console.error('Error fetching company details:', error);
      toast.error('Kunde inte hämta företagsinformation');
    } finally {
      setLoading(false);
    }
  };

  const handleUploadSuccess = () => {
    setShowUploadDialog(false);
    fetchCompanyDetails(); // Refresh data after successful upload
    toast.success('Dokument uppladdad framgångsrikt!');
  };

  const getSectorDisplayName = (sectorKey: string) => {
    return SECTORS[sectorKey as keyof typeof SECTORS] || sectorKey;
  };

  const getExchangeDisplayName = (exchangeKey: string) => {
    return STOCK_EXCHANGES[exchangeKey as keyof typeof STOCK_EXCHANGES] || exchangeKey;
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (!company) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Företaget kunde inte hittas</p>
        <Link to="/">
          <Button className="mt-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Tillbaka till översikt
          </Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with navigation */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link to="/">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Tillbaka
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{company.name}</h1>
            <p className="text-gray-600">Företagsanalys och dokumenthantering</p>
          </div>
        </div>
        <Link to={`/edit-company/${company.id}`}>
          <Button variant="outline">
            Redigera företag
          </Button>
        </Link>
      </div>

      {/* Company Overview Card */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="text-xl">{company.name}</CardTitle>
              <CardDescription className="font-mono text-lg">
                {company.ticker}
              </CardDescription>
            </div>
            <div className="text-right space-y-2">
              <Badge variant="secondary" className="text-sm">
                {getSectorDisplayName(company.sector)}
              </Badge>
              <Badge variant="outline" className="block text-sm">
                {getExchangeDisplayName(company.listing_exchange)}
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-2">
              <p className="text-sm text-gray-500">Noteringsdatum</p>
              <p className="font-medium">
                {new Date(company.listing_date).toLocaleDateString('sv-SE')}
              </p>
            </div>
            <div className="space-y-2">
              <p className="text-sm text-gray-500">Tillagd i systemet</p>
              <p className="font-medium">
                {new Date(company.created_at).toLocaleDateString('sv-SE')}
              </p>
            </div>
            <div className="space-y-2">
              <p className="text-sm text-gray-500">Senast uppdaterad</p>
              <p className="font-medium">
                {new Date(company.updated_at).toLocaleDateString('sv-SE')}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Översikt</TabsTrigger>
          <TabsTrigger value="documents">Dokument</TabsTrigger>
          <TabsTrigger value="financial">Finansiell data</TabsTrigger>
          <TabsTrigger value="analysis">Riskanalys</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <FileText className="h-5 w-5 mr-2" />
                  Dokumentstatus
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Kvartalsrapporter:</span>
                    <Badge variant="outline">
                      {documents.filter(d => d.document_type === 'quarterly').length} st
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Årsrapporter:</span>
                    <Badge variant="outline">
                      {documents.filter(d => d.document_type === 'annual').length} st
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Totalt dokument:</span>
                    <Badge variant="secondary">{documents.length} st</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <TrendingUp className="h-5 w-5 mr-2" />
                  Senaste aktivitet
                </CardTitle>
              </CardHeader>
              <CardContent>
                {documents.length > 0 ? (
                  <div className="space-y-2">
                    <p className="text-sm text-gray-600">
                      Senaste dokument: {documents[0]?.original_filename}
                    </p>
                    <p className="text-xs text-gray-500">
                      Uppladdad: {new Date(documents[0]?.uploaded_at).toLocaleDateString('sv-SE')}
                    </p>
                  </div>
                ) : (
                  <p className="text-sm text-gray-500">Inga dokument uppladdade än</p>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="documents" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span className="flex items-center">
                  <Upload className="h-5 w-5 mr-2" />
                  Dokumenthantering
                </span>
                <Dialog open={showUploadDialog} onOpenChange={setShowUploadDialog}>
                  <DialogTrigger asChild>
                    <Button>
                      <Upload className="h-4 w-4 mr-2" />
                      Ladda upp dokument
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-2xl">
                    <DialogHeader>
                      <DialogTitle>Ladda upp dokument för {company.name}</DialogTitle>
                      <DialogDescription>
                        Ladda upp kvartals- eller årsrapporter i Excel-format för automatisk bearbetning av finansiell data.
                      </DialogDescription>
                    </DialogHeader>
                    <DocumentUpload
                      companyId={company.id}
                      onSuccess={handleUploadSuccess}
                    />
                  </DialogContent>
                </Dialog>
              </CardTitle>
              <CardDescription>
                Hantera kvartals- och årsrapporter för {company.name}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {documents.length > 0 ? (
                <div className="space-y-4">
                  {documents.map((doc) => (
                    <div key={doc.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-4">
                        <FileText className="h-8 w-8 text-gray-400" />
                        <div>
                          <p className="font-medium">{doc.original_filename}</p>
                          <div className="flex items-center space-x-2 text-sm text-gray-500">
                            <Badge variant={doc.document_type === 'quarterly' ? 'default' : 'secondary'}>
                              {doc.document_type === 'quarterly' ? 'Kvartal' : 'År'}
                            </Badge>
                            <span>•</span>
                            <span>{doc.period}</span>
                            <span>•</span>
                            <span>{new Date(doc.uploaded_at).toLocaleDateString('sv-SE')}</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant={
                          doc.processing_status === 'processed' ? 'default' :
                          doc.processing_status === 'error' ? 'destructive' : 'secondary'
                        }>
                          {doc.processing_status === 'processed' ? 'Bearbetad' :
                           doc.processing_status === 'error' ? 'Fel' : 'Väntar'}
                        </Badge>
                        <Button variant="outline" size="sm">
                          Visa
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Inga dokument uppladdade</h3>
                  <p className="text-gray-500 mb-4">
                    Kom igång genom att ladda upp kvartals- eller årsrapporter
                  </p>
                  <Dialog open={showUploadDialog} onOpenChange={setShowUploadDialog}>
                    <DialogTrigger asChild>
                      <Button>
                        <Upload className="h-4 w-4 mr-2" />
                        Ladda upp första dokumentet
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-2xl">
                      <DialogHeader>
                        <DialogTitle>Ladda upp dokument för {company.name}</DialogTitle>
                        <DialogDescription>
                          Ladda upp kvartals- eller årsrapporter i Excel-format för automatisk bearbetning av finansiell data.
                        </DialogDescription>
                      </DialogHeader>
                      <DocumentUpload
                        companyId={company.id}
                        onSuccess={handleUploadSuccess}
                      />
                    </DialogContent>
                  </Dialog>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="financial" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Finansiell data</CardTitle>
              <CardDescription>
                Extraherad finansiell information från uppladdade dokument
              </CardDescription>
            </CardHeader>
            <CardContent>
              {financialData.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Period
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Typ
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Omsättning
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          EBIT
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Likvida medel
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {financialData.map((data) => (
                        <tr key={data.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {data.period}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <Badge variant={data.data_type === 'quarterly' ? 'default' : 'secondary'}>
                              {data.data_type === 'quarterly' ? 'Kvartal' : 'År'}
                            </Badge>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {data.revenue ? new Intl.NumberFormat('sv-SE', {
                              style: 'currency',
                              currency: 'SEK',
                              notation: 'compact'
                            }).format(Number(data.revenue)) : '-'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {data.ebit ? new Intl.NumberFormat('sv-SE', {
                              style: 'currency',
                              currency: 'SEK',
                              notation: 'compact'
                            }).format(Number(data.ebit)) : '-'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {data.cash_and_equivalents ? new Intl.NumberFormat('sv-SE', {
                              style: 'currency',
                              currency: 'SEK',
                              notation: 'compact'
                            }).format(Number(data.cash_and_equivalents)) : '-'}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-12">
                  <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Ingen finansiell data</h3>
                  <p className="text-gray-500">
                    Finansiell data kommer att visas här när dokument har bearbetats
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analysis" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <AlertTriangle className="h-5 w-5 mr-2" />
                Riskanalys
              </CardTitle>
              <CardDescription>
                Automatisk riskbedömning baserad på finansiell data
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <AlertTriangle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Riskanalys kommer snart</h3>
                <p className="text-gray-500">
                  Automatisk riskbedömning och rekommendationer kommer att implementeras i nästa fas
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}