import * as XLSX from 'xlsx';

interface FinancialData {
  revenue?: number;
  ebit?: number;
  net_income?: number;
  total_assets?: number;
  total_liabilities?: number;
  equity?: number;
  cash_and_equivalents?: number;
  current_assets?: number;
  current_liabilities?: number;
  long_term_debt?: number;
}

interface ProcessedDocument {
  financialData: FinancialData;
  extractedFields: string[];
  confidence: number;
}

// Common Swedish financial terms and their English equivalents
const FINANCIAL_TERMS_MAP = {
  // Revenue terms
  'omsättning': 'revenue',
  'nettoomsättning': 'revenue',
  'intäkter': 'revenue',
  'försäljning': 'revenue',
  'revenue': 'revenue',
  'sales': 'revenue',
  
  // EBIT terms
  'ebit': 'ebit',
  'rörelseresultat': 'ebit',
  'operating_income': 'ebit',
  'operating_result': 'ebit',
  
  // Net income terms
  'nettoresultat': 'net_income',
  'årets resultat': 'net_income',
  'net_income': 'net_income',
  'net_result': 'net_income',
  
  // Assets terms
  'totala tillgångar': 'total_assets',
  'summa tillgångar': 'total_assets',
  'total_assets': 'total_assets',
  'tillgångar': 'total_assets',
  
  // Liabilities terms
  'totala skulder': 'total_liabilities',
  'summa skulder': 'total_liabilities',
  'total_liabilities': 'total_liabilities',
  'skulder': 'total_liabilities',
  
  // Equity terms
  'eget kapital': 'equity',
  'equity': 'equity',
  'shareholders_equity': 'equity',
  
  // Cash terms
  'kassa och bank': 'cash_and_equivalents',
  'likvida medel': 'cash_and_equivalents',
  'cash': 'cash_and_equivalents',
  'cash_and_equivalents': 'cash_and_equivalents',
  
  // Current assets
  'omsättningstillgångar': 'current_assets',
  'current_assets': 'current_assets',
  
  // Current liabilities
  'kortfristiga skulder': 'current_liabilities',
  'current_liabilities': 'current_liabilities',
  
  // Long-term debt
  'långfristiga skulder': 'long_term_debt',
  'long_term_debt': 'long_term_debt',
  'långfristig skuld': 'long_term_debt'
};

export async function processExcelFile(file: File): Promise<ProcessedDocument> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: 'array' });
        
        // Process all sheets to find financial data
        let bestMatch: ProcessedDocument = {
          financialData: {},
          extractedFields: [],
          confidence: 0
        };
        
        for (const sheetName of workbook.SheetNames) {
          const worksheet = workbook.Sheets[sheetName];
          const processed = extractFinancialDataFromSheet(worksheet);
          
          if (processed.confidence > bestMatch.confidence) {
            bestMatch = processed;
          }
        }
        
        resolve(bestMatch);
      } catch (error) {
        reject(new Error('Failed to process Excel file: ' + error));
      }
    };
    
    reader.onerror = () => reject(new Error('Failed to read file'));
    reader.readAsArrayBuffer(file);
  });
}

function extractFinancialDataFromSheet(worksheet: XLSX.WorkSheet): ProcessedDocument {
  const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1:Z100');
  const financialData: FinancialData = {};
  const extractedFields: string[] = [];
  let confidence = 0;
  
  // Convert worksheet to array format for easier processing
  const data: any[][] = [];
  for (let row = range.s.r; row <= range.e.r; row++) {
    const rowData: any[] = [];
    for (let col = range.s.c; col <= range.e.c; col++) {
      const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
      const cell = worksheet[cellAddress];
      rowData.push(cell ? cell.v : null);
    }
    data.push(rowData);
  }
  
  // Search for financial terms and their corresponding values
  for (let row = 0; row < data.length; row++) {
    for (let col = 0; col < data[row].length; col++) {
      const cellValue = data[row][col];
      
      if (typeof cellValue === 'string') {
        const normalizedValue = cellValue.toLowerCase().trim();
        
        // Check if this cell contains a financial term
        for (const [term, field] of Object.entries(FINANCIAL_TERMS_MAP)) {
          if (normalizedValue.includes(term)) {
            // Look for numeric values in adjacent cells
            const numericValue = findAdjacentNumericValue(data, row, col);
            
            if (numericValue !== null && Math.abs(numericValue) > 1000) { // Reasonable threshold
              financialData[field as keyof FinancialData] = numericValue;
              extractedFields.push(term);
              confidence += 10; // Increase confidence for each field found
            }
          }
        }
      }
    }
  }
  
  // Normalize confidence score (0-100)
  confidence = Math.min(confidence, 100);
  
  return {
    financialData,
    extractedFields,
    confidence
  };
}

function findAdjacentNumericValue(data: any[][], row: number, col: number): number | null {
  // Search in adjacent cells for numeric values
  const searchOffsets = [
    [0, 1], [0, 2], [0, 3], // Right
    [1, 0], [2, 0], [3, 0], // Down
    [0, -1], [0, -2], [0, -3], // Left
    [-1, 0], [-2, 0], [-3, 0], // Up
    [1, 1], [1, -1], [-1, 1], [-1, -1] // Diagonal
  ];
  
  for (const [rowOffset, colOffset] of searchOffsets) {
    const newRow = row + rowOffset;
    const newCol = col + colOffset;
    
    if (newRow >= 0 && newRow < data.length && 
        newCol >= 0 && newCol < data[newRow].length) {
      
      const value = data[newRow][newCol];
      
      if (typeof value === 'number' && !isNaN(value)) {
        return value;
      }
      
      // Try to parse string numbers
      if (typeof value === 'string') {
        const cleanValue = value.replace(/[^\d.-]/g, '');
        const numValue = parseFloat(cleanValue);
        if (!isNaN(numValue)) {
          return numValue;
        }
      }
    }
  }
  
  return null;
}

export function calculateRiskScore(financialData: FinancialData): {
  liquidityRisk: number;
  debtRisk: number;
  profitabilityRisk: number;
  marketRisk: number;
  overallScore: number;
  riskCategory: string;
} {
  let liquidityRisk = 0;
  let debtRisk = 0;
  let profitabilityRisk = 0;
  let marketRisk = 0;
  
  // Liquidity Risk (40% weight)
  if (financialData.current_assets && financialData.current_liabilities) {
    const currentRatio = financialData.current_assets / financialData.current_liabilities;
    if (currentRatio < 1.0) liquidityRisk = 5;
    else if (currentRatio < 1.2) liquidityRisk = 4;
    else if (currentRatio < 1.5) liquidityRisk = 3;
    else if (currentRatio < 2.0) liquidityRisk = 2;
    else liquidityRisk = 1;
  }
  
  // Debt Risk (30% weight)
  if (financialData.total_liabilities && financialData.equity) {
    const debtToEquityRatio = financialData.total_liabilities / financialData.equity;
    if (debtToEquityRatio > 2.0) debtRisk = 5;
    else if (debtToEquityRatio > 1.5) debtRisk = 4;
    else if (debtToEquityRatio > 1.0) debtRisk = 3;
    else if (debtToEquityRatio > 0.5) debtRisk = 2;
    else debtRisk = 1;
  }
  
  // Profitability Risk (20% weight)
  if (financialData.ebit && financialData.revenue) {
    const ebitMargin = financialData.ebit / financialData.revenue;
    if (ebitMargin < -0.1) profitabilityRisk = 5;
    else if (ebitMargin < 0) profitabilityRisk = 4;
    else if (ebitMargin < 0.05) profitabilityRisk = 3;
    else if (ebitMargin < 0.1) profitabilityRisk = 2;
    else profitabilityRisk = 1;
  }
  
  // Market Risk (10% weight) - simplified
  marketRisk = 3; // Default moderate risk
  
  // Calculate weighted overall score
  const overallScore = (
    liquidityRisk * 0.4 +
    debtRisk * 0.3 +
    profitabilityRisk * 0.2 +
    marketRisk * 0.1
  );
  
  let riskCategory = 'Låg risk';
  if (overallScore >= 4.0) riskCategory = 'Hög risk';
  else if (overallScore >= 3.0) riskCategory = 'Medelhög risk';
  else if (overallScore >= 2.0) riskCategory = 'Måttlig risk';
  
  return {
    liquidityRisk,
    debtRisk,
    profitabilityRisk,
    marketRisk,
    overallScore,
    riskCategory
  };
}
