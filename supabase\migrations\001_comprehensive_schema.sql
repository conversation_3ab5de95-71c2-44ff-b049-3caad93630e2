-- Corporate Finance Opportunity Analyzer - Comprehensive Database Schema
-- This migration creates the complete database structure for the Swedish small-cap analyzer

-- Drop existing tables if they exist (for clean migration)
DROP TABLE IF EXISTS service_opportunities CASCADE;
DROP TABLE IF EXISTS quarterly_reports CASCADE;
DROP TABLE IF EXISTS financial_data CASCADE;
DROP TABLE IF EXISTS company_documents CASCADE;
DROP TABLE IF EXISTS companies CASCADE;

-- Companies table with comprehensive fields
CREATE TABLE companies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    ticker TEXT UNIQUE NOT NULL,
    sector TEXT NOT NULL,
    listing_exchange TEXT NOT NULL,
    listing_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Document storage per company
CREATE TABLE company_documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID REFERENCES companies(id) ON DELETE CASCADE,
    document_type TEXT CHECK (document_type IN ('quarterly', 'annual')) NOT NULL,
    period TEXT NOT NULL, -- Format: 'Q1 2024', 'Q2 2024', '2024'
    file_path TEXT NOT NULL,
    original_filename TEXT NOT NULL,
    file_size INTEGER,
    uploaded_at TIMESTAMP DEFAULT NOW(),
    processed_at TIMESTAMP,
    processing_status TEXT DEFAULT 'pending' CHECK (processing_status IN ('pending', 'processed', 'error'))
);

-- Extracted financial data
CREATE TABLE financial_data (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID REFERENCES companies(id) ON DELETE CASCADE,
    document_id UUID REFERENCES company_documents(id) ON DELETE CASCADE,
    period TEXT NOT NULL,
    data_type TEXT CHECK (data_type IN ('quarterly', 'annual')) NOT NULL,
    -- Financial metrics (all DECIMAL for precision)
    revenue DECIMAL(15,2),
    ebit DECIMAL(15,2),
    net_income DECIMAL(15,2),
    total_assets DECIMAL(15,2),
    total_liabilities DECIMAL(15,2),
    equity DECIMAL(15,2),
    cash_and_equivalents DECIMAL(15,2),
    current_assets DECIMAL(15,2),
    current_liabilities DECIMAL(15,2),
    long_term_debt DECIMAL(15,2),
    -- Timestamps
    extracted_at TIMESTAMP DEFAULT NOW(),
    -- Ensure unique combination
    UNIQUE(company_id, period, data_type)
);

-- Service opportunities (enhanced)
CREATE TABLE service_opportunities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID REFERENCES companies(id) ON DELETE CASCADE,
    opportunity_type TEXT CHECK (opportunity_type IN ('NEW_ISSUE', 'CONVERTIBLE', 'DEBT_RESTRUCTURING', 'M&A')) NOT NULL,
    confidence_score DECIMAL(3,2) CHECK (confidence_score >= 0 AND confidence_score <= 5),
    reasoning TEXT,
    risk_category TEXT,
    recommendations TEXT[],
    created_at TIMESTAMP DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_companies_ticker ON companies(ticker);
CREATE INDEX idx_companies_sector ON companies(sector);
CREATE INDEX idx_companies_listing_exchange ON companies(listing_exchange);
CREATE INDEX idx_company_documents_company_id ON company_documents(company_id);
CREATE INDEX idx_company_documents_period ON company_documents(period);
CREATE INDEX idx_financial_data_company_id ON financial_data(company_id);
CREATE INDEX idx_financial_data_period ON financial_data(period);
CREATE INDEX idx_service_opportunities_company_id ON service_opportunities(company_id);

-- Row Level Security (RLS) policies
ALTER TABLE companies ENABLE ROW LEVEL SECURITY;
ALTER TABLE company_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE financial_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE service_opportunities ENABLE ROW LEVEL SECURITY;

-- Basic policies (allow all operations for now - can be restricted later)
CREATE POLICY "Allow all operations on companies" ON companies FOR ALL USING (true);
CREATE POLICY "Allow all operations on company_documents" ON company_documents FOR ALL USING (true);
CREATE POLICY "Allow all operations on financial_data" ON financial_data FOR ALL USING (true);
CREATE POLICY "Allow all operations on service_opportunities" ON service_opportunities FOR ALL USING (true);

-- Storage bucket for company documents
INSERT INTO storage.buckets (id, name, public) VALUES ('company-documents', 'company-documents', false);

-- Storage policies
CREATE POLICY "Allow all operations on company-documents bucket" ON storage.objects FOR ALL USING (bucket_id = 'company-documents');
