import * as XLSX from 'xlsx';
import { QuarterlyReport, Company } from './supabase';

interface ExcelRow {
  'Bolagsnamn': string;
  'Ticker': string;
  'Period/Kvartal': string;
  'Omsättning (SEK)': number;
  'EBIT/EBITDA (SEK)': number;
  'Totala tillgångar': number;
  'Kortfristiga skulder': number;
  'Långfristiga skulder': number;
  'Likvida medel': number;
  'Rörelsekapital': number;
  'Antal utestående aktier': number;
  'Börsvärde': number;
}

export async function processExcelFile(file: File, companyId: string): Promise<{
  quarterlyReport: QuarterlyReport;
  serviceOpportunityScore: number;
}> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = (e) => {
      try {
        const data = e.target?.result;
        const workbook = XLSX.read(data, { type: 'binary' });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json<ExcelRow>(worksheet);

        if (jsonData.length === 0) {
          throw new Error('Excel-filen innehåller ingen data');
        }

        const row = jsonData[0]; // Process first row only

        // Validate required fields
        const requiredFields = [
          'Bolagsnamn',
          'Ticker',
          'Period/Kvartal',
          'Omsättning (SEK)',
          'EBIT/EBITDA (SEK)',
          'Totala tillgångar',
          'Kortfristiga skulder',
          'Långfristiga skulder',
          'Likvida medel'
        ];

        for (const field of requiredFields) {
          if (row[field as keyof ExcelRow] === undefined) {
            throw new Error(`Saknad kolumn: ${field}`);
          }
        }

        // Convert to QuarterlyReport format
        const quarterlyReport: QuarterlyReport = {
          id: '', // Will be set by Supabase
          company_id: companyId,
          quarter: row['Period/Kvartal'],
          revenue: row['Omsättning (SEK)'],
          ebit: row['EBIT/EBITDA (SEK)'],
          total_assets: row['Totala tillgångar'],
          current_liabilities: row['Kortfristiga skulder'],
          liquid_assets: row['Likvida medel'],
          total_debt: (row['Kortfristiga skulder'] || 0) + (row['Långfristiga skulder'] || 0),
          operating_cash_flow: row['Rörelsekapital'] || 0,
          net_income: row['EBIT/EBITDA (SEK)'] || 0,
          uploaded_at: new Date().toISOString()
        };

        // Calculate service opportunity score
        const serviceOpportunityScore = calculateServiceOpportunityScore(quarterlyReport);

        resolve({
          quarterlyReport,
          serviceOpportunityScore
        });
      } catch (error) {
        reject(error);
      }
    };

    reader.onerror = () => {
      reject(new Error('Kunde inte läsa filen'));
    };

    reader.readAsBinaryString(file);
  });
}

function calculateServiceOpportunityScore(report: QuarterlyReport): number {
  let score = 0;

  // Liquidity analysis (40% weight)
  const currentRatio = report.total_assets / report.current_liabilities;
  const quickRatio = report.liquid_assets / report.current_liabilities;
  const cashToDebt = report.liquid_assets / report.total_debt;

  if (currentRatio < 1.0) score += 20;
  if (quickRatio < 0.5) score += 20;
  if (cashToDebt < 0.2) score += 20;

  // Leverage analysis (30% weight)
  const debtToEquity = report.total_debt / (report.total_assets - report.total_debt);
  const debtToAssets = report.total_debt / report.total_assets;

  if (debtToEquity > 2.0) score += 15;
  if (debtToAssets > 0.7) score += 15;

  // Profitability analysis (30% weight)
  const profitMargin = report.ebit / report.revenue;
  const operatingMargin = report.operating_cash_flow / report.revenue;

  if (profitMargin < 0) score += 15;
  if (operatingMargin < 0) score += 15;

  return Math.min(Math.round(score), 100);
} 