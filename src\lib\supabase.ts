import { createClient } from '@supabase/supabase-js';

// Enhanced types for comprehensive database schema
export type Company = {
  id: string;
  name: string;
  ticker: string;
  sector: string;
  listing_exchange: string;
  listing_date: string;
  created_at: string;
  updated_at: string;
};

export type CompanyDocument = {
  id: string;
  company_id: string;
  document_type: 'quarterly' | 'annual';
  period: string; // Format: 'Q1 2024', 'Q2 2024', '2024'
  file_path: string;
  original_filename: string;
  file_size: number;
  uploaded_at: string;
  processed_at?: string;
  processing_status: 'pending' | 'processed' | 'error';
};

export type FinancialData = {
  id: string;
  company_id: string;
  document_id: string;
  period: string;
  data_type: 'quarterly' | 'annual';
  // Financial metrics
  revenue?: number;
  ebit?: number;
  net_income?: number;
  total_assets?: number;
  total_liabilities?: number;
  equity?: number;
  cash_and_equivalents?: number;
  current_assets?: number;
  current_liabilities?: number;
  long_term_debt?: number;
  extracted_at: string;
};

export type ServiceOpportunity = {
  id: string;
  company_id: string;
  opportunity_type: 'NEW_ISSUE' | 'CONVERTIBLE' | 'DEBT_RESTRUCTURING' | 'M&A';
  confidence_score: number;
  reasoning: string;
  created_at: string;
};

// Initialize Supabase client
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Storage bucket names
export const STORAGE_BUCKETS = {
  COMPANY_DOCUMENTS: 'company-documents',
} as const;

// Database table names
export const TABLES = {
  COMPANIES: 'companies',
  COMPANY_DOCUMENTS: 'company_documents',
  FINANCIAL_DATA: 'financial_data',
  SERVICE_OPPORTUNITIES: 'service_opportunities',
} as const;

// Swedish stock exchanges
export const STOCK_EXCHANGES = {
  SPOTLIGHT: 'Spotlight Stock Market',
  NGM: 'NGM Nordic',
  FIRST_NORTH: 'First North',
  NASDAQ_STOCKHOLM: 'Nasdaq Stockholm',
  OTHER: 'Annan börs',
} as const;

// Industry sectors for Swedish small-cap companies
export const SECTORS = {
  TECHNOLOGY: 'Teknik',
  HEALTHCARE: 'Hälsovård',
  INDUSTRIALS: 'Industri',
  CONSUMER_GOODS: 'Konsumentvaror',
  FINANCIALS: 'Finans',
  REAL_ESTATE: 'Fastigheter',
  ENERGY: 'Energi',
  MATERIALS: 'Material',
  TELECOMMUNICATIONS: 'Telekommunikation',
  UTILITIES: 'Allmännyttiga tjänster',
  OTHER: 'Övrigt',
} as const;

// Service opportunity types
export const OPPORTUNITY_TYPES = {
  NEW_ISSUE: 'Nyemission',
  CONVERTIBLE: 'Konvertibelt lån',
  DEBT_RESTRUCTURING: 'Skuldsanering',
  M_A: 'Fusion & Förvärv',
} as const; 