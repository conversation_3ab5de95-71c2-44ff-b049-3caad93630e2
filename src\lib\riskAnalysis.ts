import { QuarterlyData } from './supabase';

interface RiskScores {
  liquidity_risk: number;
  leverage_risk: number;
  profitability_risk: number;
  total_risk_score: number;
  risk_category: string;
  recommendations: string[];
}

export function calculateRiskAnalysis(data: QuarterlyData): RiskScores {
  // Calculate liquidity risk (40% weight)
  const currentRatio = data.total_assets / data.current_liabilities;
  const quickRatio = data.liquid_assets / data.current_liabilities;
  
  let liquidityRisk = 0;
  if (currentRatio < 1.0) liquidityRisk += 30;
  if (quickRatio < 0.5) liquidityRisk += 20;
  if (data.liquid_assets < data.current_liabilities) liquidityRisk += 50;

  // Calculate leverage risk (30% weight)
  const debtToEquity = data.total_debt / (data.total_assets - data.total_debt);
  const debtToAssets = data.total_debt / data.total_assets;
  
  let leverageRisk = 0;
  if (debtToEquity > 2.0) leverageRisk += 40;
  if (debtToAssets > 0.7) leverageRisk += 30;
  if (data.total_debt > data.total_assets) leverageRisk += 30;

  // Calculate profitability risk (20% weight)
  const profitMargin = data.ebit / data.revenue;
  
  let profitabilityRisk = 0;
  if (profitMargin < 0) profitabilityRisk += 50;
  if (profitMargin < 0.05) profitabilityRisk += 30;
  if (data.ebit < 0) profitabilityRisk += 20;

  // Calculate total risk score
  const totalRiskScore = Math.round(
    (liquidityRisk * 0.4) + (leverageRisk * 0.3) + (profitabilityRisk * 0.3)
  );

  // Determine risk category
  let riskCategory = 'LOW';
  if (totalRiskScore >= 70) riskCategory = 'HIGH';
  else if (totalRiskScore >= 40) riskCategory = 'MEDIUM';

  // Generate recommendations
  const recommendations: string[] = [];

  // Liquidity recommendations
  if (liquidityRisk > 50) {
    recommendations.push('Överväg nyemission för att stärka likviditeten');
  } else if (liquidityRisk > 30) {
    recommendations.push('Utvärdera möjligheter till konvertibelt lån');
  }

  // Leverage recommendations
  if (leverageRisk > 50) {
    recommendations.push('Prioritera skuldsanering och omstrukturering av skulder');
  } else if (leverageRisk > 30) {
    recommendations.push('Utvärdera möjligheter till skuldförlängning');
  }

  // Profitability recommendations
  if (profitabilityRisk > 50) {
    recommendations.push('Genomför kostnadsbesparande åtgärder och effektiviseringar');
  } else if (profitabilityRisk > 30) {
    recommendations.push('Utvärdera prisstrategi och produktmix');
  }

  return {
    liquidity_risk: liquidityRisk,
    leverage_risk: leverageRisk,
    profitability_risk: profitabilityRisk,
    total_risk_score: totalRiskScore,
    risk_category: riskCategory,
    recommendations
  };
} 