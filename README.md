# Swedish Corporate Finance Opportunity Analyzer

A comprehensive web application for analyzing Swedish small-cap companies and identifying corporate finance opportunities. Built with React, TypeScript, and Supabase, optimized for Loveable.dev hosting.

## 🚀 Features

- **Company Portfolio Management**: Track Swedish small-cap companies across different exchanges
- **Financial Data Analysis**: Upload and analyze quarterly/annual reports
- **Opportunity Identification**: Automated detection of corporate finance opportunities
- **Swedish Market Focus**: Tailored for Swedish stock exchanges and business regulations
- **Modern UI**: Responsive design with shadcn/ui components

## 🏗️ Tech Stack

- **Frontend**: React 18, TypeScript, Vite
- **Styling**: Tailwind CSS, shadcn/ui
- **Database**: Supabase (PostgreSQL)
- **Hosting**: Loveable.dev
- **Charts**: Recharts
- **Forms**: React Hook Form + Zod

## 🌐 Loveable.dev Deployment

This project is configured for seamless deployment on Loveable.dev:

### Environment Variables
Set these in your Loveable.dev project settings:

```
VITE_SUPABASE_URL=https://zxaxmmrlfwnvstthdbqf.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp4YXhtbXJsZndudnN0dGhkYnFmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk0ODU3MjUsImV4cCI6MjA2NTA2MTcyNX0.G2JFEEg0K9vtnFUDa17zVdy71r9C7_Ky8f5vzm-YyyU
```

### Build Configuration
- ✅ `build:dev` script included for Loveable.dev
- ✅ Vite configured with host "::" and port 8080
- ✅ Fallback environment variables included in code

## 🛠️ Local Development

1. Clone the repository:
```bash
git clone https://github.com/MarkusMBH/svensk-bors-blick.git
cd svensk-bors-blick
```

2. Install dependencies:
```bash
npm install
```

3. Copy environment variables:
```bash
cp .env.example .env
```

4. Start development server:
```bash
npm run dev
```

## 📊 Database Schema

The application uses a comprehensive PostgreSQL schema:

- **companies**: Core company information with Swedish market context
- **company_documents**: Document storage metadata for quarterly/annual reports
- **financial_data**: Extracted financial metrics with precision decimal fields
- **service_opportunities**: Business opportunity tracking and recommendations

### Database Migration
Run the included migration script to set up the complete schema:

```bash
npm run migrate
```

The database includes:
- ✅ All required tables with proper relationships
- ✅ Indexes for optimal performance
- ✅ Row Level Security (RLS) policies
- ✅ Storage bucket for document uploads
- ✅ Sample Swedish company data

## 🇸🇪 Swedish Market Context

- **Stock Exchanges**: Spotlight Stock Market, NGM Nordic, First North, Nasdaq Stockholm
- **Sectors**: Technology, Healthcare, Industrials, Consumer Goods, Financials, Real Estate
- **Terminology**: Swedish business terms throughout the interface
- **Compliance**: Follows Swedish corporate finance regulations

## 🚀 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run build:dev` - Build for development (Loveable.dev)
- `npm run lint` - Run ESLint
- `npm run preview` - Preview production build
- `npm run migrate` - Run database migration

## 📈 Current Status: Phase 2 Complete

✅ **Frontend Application**: Corporate finance analyzer dashboard operational
✅ **Database Schema**: Complete schema with all required tables
✅ **Loveable.dev Ready**: Configured for seamless deployment
✅ **Swedish Context**: Market-specific terminology and regulations
✅ **Sample Data**: Swedish companies included for testing

## 📝 License

MIT
