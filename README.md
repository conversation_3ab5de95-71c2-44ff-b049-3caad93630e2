# Corporate Finance Opportunity Analyzer

En webbapplikation för att analysera kvartalsrapporter från svenska småbolagsbörser (NGM Spotlight, First North) för att identifiera bolag med behov av nyemissioner, konvertibla lån och corporate finance-tj<PERSON>nster.

## Funktioner

- Excel-filuppladdning för kvartalsrapporter
- Automatisk finansiell riskanalys
- Regelbaserad analys av likviditet, skuldsättning och lönsamhet
- Rekommendationer för corporate finance-tjänster
- Responsivt användargränssnitt

## Teknisk Stack

- React.js med TypeScript
- Supabase (Database, Storage, Auth)
- Tailwind CSS för styling
- XLSX för Excel-filbehandling

## Installation

1. Klona projektet:
```bash
git clone [repository-url]
cd corporate-finance-analyzer
```

2. Installera beroenden:
```bash
npm install
```

3. Skapa en `.env` fil i projektets rot och lägg till dina Supabase-uppgifter:
```env
VITE_SUPABASE_URL=din_supabase_url
VITE_SUPABASE_ANON_KEY=din_supabase_anon_key
```

4. Starta utvecklingsservern:
```bash
npm run dev
```

## Supabase Setup

1. Skapa ett nytt projekt på [Supabase](https://supabase.com)
2. Skapa följande tabeller i databasen:

```sql
-- Företagstabell
CREATE TABLE companies (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    ticker TEXT UNIQUE,
    sector TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Kvartalsdata
CREATE TABLE quarterly_data (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    company_id UUID REFERENCES companies(id),
    quarter TEXT NOT NULL,
    revenue DECIMAL,
    ebit DECIMAL,
    total_assets DECIMAL,
    current_liabilities DECIMAL,
    liquid_assets DECIMAL,
    total_debt DECIMAL,
    uploaded_at TIMESTAMP DEFAULT NOW()
);

-- Riskanalys
CREATE TABLE risk_analysis (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    company_id UUID REFERENCES companies(id),
    quarter TEXT,
    liquidity_risk INTEGER,
    leverage_risk INTEGER,
    profitability_risk INTEGER,
    total_risk_score INTEGER,
    risk_category TEXT,
    recommendations TEXT[],
    created_at TIMESTAMP DEFAULT NOW()
);
```

3. Skapa en Storage bucket för Excel-filer:
   - Namn: `financial-reports`
   - Åtkomst: Privat

## Excel-filformat

Excel-filen ska innehålla följande kolumner:

- Bolagsnamn
- Ticker
- Period/Kvartal
- Omsättning (SEK)
- EBIT/EBITDA (SEK)
- Totala tillgångar
- Kortfristiga skulder
- Långfristiga skulder
- Likvida medel
- Rörelsekapital
- Antal utestående aktier
- Börsvärde

## Utveckling

- `npm run dev` - Starta utvecklingsservern
- `npm run build` - Bygg för produktion
- `npm run lint` - Kör linting
- `npm run preview` - Förhandsgranska produktionsbyggen

## Licens

MIT
