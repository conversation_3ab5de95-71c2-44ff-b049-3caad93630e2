export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      companies: {
        Row: {
          created_at: string | null
          id: string
          listing_date: string
          listing_exchange: string
          name: string
          sector: string
          ticker: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          listing_date: string
          listing_exchange: string
          name: string
          sector: string
          ticker: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          listing_date?: string
          listing_exchange?: string
          name?: string
          sector?: string
          ticker?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      company_documents: {
        Row: {
          company_id: string | null
          document_type: string
          file_path: string
          file_size: number | null
          id: string
          original_filename: string
          period: string
          processed_at: string | null
          processing_status: string | null
          uploaded_at: string | null
        }
        Insert: {
          company_id?: string | null
          document_type: string
          file_path: string
          file_size?: number | null
          id?: string
          original_filename: string
          period: string
          processed_at?: string | null
          processing_status?: string | null
          uploaded_at?: string | null
        }
        Update: {
          company_id?: string | null
          document_type?: string
          file_path?: string
          file_size?: number | null
          id?: string
          original_filename?: string
          period?: string
          processed_at?: string | null
          processing_status?: string | null
          uploaded_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "company_documents_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
        ]
      }
      financial_data: {
        Row: {
          cash_and_equivalents: number | null
          company_id: string | null
          current_assets: number | null
          current_liabilities: number | null
          data_type: string
          document_id: string | null
          ebit: number | null
          equity: number | null
          extracted_at: string | null
          id: string
          long_term_debt: number | null
          net_income: number | null
          period: string
          revenue: number | null
          total_assets: number | null
          total_liabilities: number | null
        }
        Insert: {
          cash_and_equivalents?: number | null
          company_id?: string | null
          current_assets?: number | null
          current_liabilities?: number | null
          data_type: string
          document_id?: string | null
          ebit?: number | null
          equity?: number | null
          extracted_at?: string | null
          id?: string
          long_term_debt?: number | null
          net_income?: number | null
          period: string
          revenue?: number | null
          total_assets?: number | null
          total_liabilities?: number | null
        }
        Update: {
          cash_and_equivalents?: number | null
          company_id?: string | null
          current_assets?: number | null
          current_liabilities?: number | null
          data_type?: string
          document_id?: string | null
          ebit?: number | null
          equity?: number | null
          extracted_at?: string | null
          id?: string
          long_term_debt?: number | null
          net_income?: number | null
          period?: string
          revenue?: number | null
          total_assets?: number | null
          total_liabilities?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "financial_data_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "financial_data_document_id_fkey"
            columns: ["document_id"]
            isOneToOne: false
            referencedRelation: "company_documents"
            referencedColumns: ["id"]
          },
        ]
      }
      service_opportunities: {
        Row: {
          company_id: string | null
          confidence_score: number | null
          created_at: string | null
          id: string
          opportunity_type: string
          reasoning: string | null
          recommendations: string[] | null
          risk_category: string | null
        }
        Insert: {
          company_id?: string | null
          confidence_score?: number | null
          created_at?: string | null
          id?: string
          opportunity_type: string
          reasoning?: string | null
          recommendations?: string[] | null
          risk_category?: string | null
        }
        Update: {
          company_id?: string | null
          confidence_score?: number | null
          created_at?: string | null
          id?: string
          opportunity_type?: string
          reasoning?: string | null
          recommendations?: string[] | null
          risk_category?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "service_opportunities_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
