import { Button } from './ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Download, FileSpreadsheet, Info } from 'lucide-react';
import { toast } from 'sonner';
import * as XLSX from 'xlsx';

export function ExcelTemplateHelper() {
  const downloadTemplate = () => {
    // Create a sample Excel template with Swedish financial terms
    const templateData = [
      // Headers
      ['Finansiell Information', '', '', ''],
      ['', '', '', ''],
      // Revenue section
      ['Omsättning', '', '', ''],
      ['Nettoomsättning', '1000000', 'SEK', 'Q1 2024'],
      ['Bruttoomsättning', '1100000', 'SEK', 'Q1 2024'],
      ['', '', '', ''],
      // Profitability
      ['Resultat', '', '', ''],
      ['EBIT', '150000', 'SEK', 'Q1 2024'],
      ['EBITDA', '200000', 'SEK', 'Q1 2024'],
      ['Nettoresultat', '120000', 'SEK', 'Q1 2024'],
      ['', '', '', ''],
      // Balance sheet
      ['Balansräkning', '', '', ''],
      ['Totala tillgångar', '5000000', 'SEK', 'Q1 2024'],
      ['Totala skulder', '2000000', 'SEK', 'Q1 2024'],
      ['Eget kapital', '3000000', 'SEK', 'Q1 2024'],
      ['', '', '', ''],
      // Liquidity
      ['Likviditet', '', '', ''],
      ['Likvida medel', '500000', 'SEK', 'Q1 2024'],
      ['Kassa och bank', '300000', 'SEK', 'Q1 2024'],
      ['Kortfristiga tillgångar', '1500000', 'SEK', 'Q1 2024'],
      ['Kortfristiga skulder', '800000', 'SEK', 'Q1 2024'],
      ['', '', '', ''],
      // Debt
      ['Skulder', '', '', ''],
      ['Långfristiga skulder', '1200000', 'SEK', 'Q1 2024'],
      ['Totala skulder', '2000000', 'SEK', 'Q1 2024'],
    ];

    // Create workbook and worksheet
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.aoa_to_sheet(templateData);

    // Set column widths
    ws['!cols'] = [
      { wch: 25 }, // Financial term column
      { wch: 15 }, // Value column
      { wch: 10 }, // Currency column
      { wch: 15 }  // Period column
    ];

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(wb, ws, 'Finansiell Data');

    // Generate and download file
    XLSX.writeFile(wb, 'finansiell_data_mall.xlsx');
    toast.success('Excel-mall nedladdad!');
  };

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="flex items-center text-lg">
          <FileSpreadsheet className="h-5 w-5 mr-2" />
          Excel-format hjälp
        </CardTitle>
        <CardDescription>
          Ladda ner en mall eller läs om vilket format som stöds
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-start space-x-3 p-3 bg-blue-50 rounded-lg">
          <Info className="h-5 w-5 text-blue-600 mt-0.5" />
          <div className="text-sm text-blue-800">
            <p className="font-medium mb-1">Stödda finansiella termer:</p>
            <p className="text-xs">
              Omsättning, EBIT, EBITDA, Nettoresultat, Totala tillgångar, Totala skulder, 
              Eget kapital, Likvida medel, Kortfristiga tillgångar, Kortfristiga skulder, 
              Långfristiga skulder, Kassa och bank
            </p>
          </div>
        </div>
        
        <Button onClick={downloadTemplate} variant="outline" className="w-full">
          <Download className="h-4 w-4 mr-2" />
          Ladda ner Excel-mall
        </Button>
      </CardContent>
    </Card>
  );
}
