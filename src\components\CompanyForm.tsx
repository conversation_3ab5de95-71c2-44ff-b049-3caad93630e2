import { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { supabase, TABLES, Company, SECTORS, STOCK_EXCHANGES } from '../lib/supabase';
import { toast } from 'sonner';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';

interface CompanyFormProps {
  onSuccess?: () => void;
  initialData?: Company;
}

export function CompanyForm({ onSuccess, initialData }: CompanyFormProps) {
  const navigate = useNavigate();
  const { id } = useParams();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: initialData?.name || '',
    ticker: initialData?.ticker || '',
    sector: initialData?.sector || '',
    listing_exchange: initialData?.listing_exchange || '',
    listing_date: initialData?.listing_date || '',
  });

  const validateForm = () => {
    if (!formData.name.trim()) {
      toast.error('Företagsnamn är obligatoriskt');
      return false;
    }
    if (!formData.ticker.trim()) {
      toast.error('Ticker är obligatoriskt');
      return false;
    }
    if (!formData.sector) {
      toast.error('Sektor är obligatorisk');
      return false;
    }
    if (!formData.listing_exchange) {
      toast.error('Börs är obligatorisk');
      return false;
    }
    if (!formData.listing_date) {
      toast.error('Noteringsdatum är obligatoriskt');
      return false;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setLoading(true);

    try {
      const companyData = {
        ...formData,
        ticker: formData.ticker.toUpperCase(), // Ensure ticker is uppercase
        updated_at: new Date().toISOString(),
      };

      if (initialData) {
        const { error } = await supabase
          .from(TABLES.COMPANIES)
          .update(companyData)
          .eq('id', initialData.id);

        if (error) throw error;
        toast.success('Företag uppdaterat');
      } else {
        const { data, error } = await supabase
          .from(TABLES.COMPANIES)
          .insert([companyData])
          .select()
          .single();

        if (error) throw error;
        toast.success('Företag tillagt');

        // Navigate to the new company's details page
        if (data) {
          navigate(`/company/${data.id}`);
          return;
        }
      }

      onSuccess?.();
    } catch (error: any) {
      console.error('Error saving company:', error);
      if (error.code === '23505') {
        toast.error('En företag med denna ticker finns redan');
      } else {
        toast.error('Kunde inte spara företag');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle>
            {initialData ? 'Redigera företag' : 'Lägg till nytt företag'}
          </CardTitle>
          <CardDescription>
            Fyll i all obligatorisk information om det svenska småbolagsföretaget.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="name">Företagsnamn *</Label>
                <Input
                  id="name"
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="t.ex. Boliden AB"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="ticker">Ticker Symbol *</Label>
                <Input
                  id="ticker"
                  type="text"
                  value={formData.ticker}
                  onChange={(e) => setFormData({ ...formData, ticker: e.target.value.toUpperCase() })}
                  placeholder="t.ex. BOL"
                  maxLength={10}
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="sector">Sektor/Bransch *</Label>
                <Select
                  value={formData.sector}
                  onValueChange={(value) => setFormData({ ...formData, sector: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Välj sektor" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(SECTORS).map(([key, value]) => (
                      <SelectItem key={key} value={key}>
                        {value}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="listing_exchange">Börs *</Label>
                <Select
                  value={formData.listing_exchange}
                  onValueChange={(value) => setFormData({ ...formData, listing_exchange: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Välj börs" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(STOCK_EXCHANGES).map(([key, value]) => (
                      <SelectItem key={key} value={key}>
                        {value}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="listing_date">Noteringsdatum *</Label>
              <Input
                id="listing_date"
                type="date"
                value={formData.listing_date}
                onChange={(e) => setFormData({ ...formData, listing_date: e.target.value })}
                required
              />
            </div>

            <div className="flex justify-end space-x-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => navigate('/')}
                disabled={loading}
              >
                Avbryt
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Sparar...
                  </>
                ) : initialData ? (
                  'Uppdatera företag'
                ) : (
                  'Lägg till företag'
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}