import { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { supabase, TABLES, STORAGE_BUCKETS } from '../lib/supabase';
import { toast } from 'sonner';
import { Button } from './ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Upload, FileText, X } from 'lucide-react';
import * as XLSX from 'xlsx';

interface DocumentUploadProps {
  companyId: string;
  onSuccess?: () => void;
}

export function DocumentUpload({ companyId, onSuccess }: DocumentUploadProps) {
  const [uploading, setUploading] = useState(false);
  const [documentType, setDocumentType] = useState<'quarterly' | 'annual'>('quarterly');
  const [period, setPeriod] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (file) {
      // Validate file type
      const validTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel',
        'text/csv'
      ];
      
      if (!validTypes.includes(file.type)) {
        toast.error('Endast Excel-filer (.xlsx, .xls) och CSV-filer är tillåtna');
        return;
      }

      // Validate file size (10MB limit)
      if (file.size > 10 * 1024 * 1024) {
        toast.error('Filen är för stor. Maximal storlek är 10MB');
        return;
      }

      setSelectedFile(file);
      
      // Auto-detect period from filename if possible
      const filename = file.name.toLowerCase();
      if (filename.includes('q1') || filename.includes('kv1')) {
        setPeriod('Q1 2024');
        setDocumentType('quarterly');
      } else if (filename.includes('q2') || filename.includes('kv2')) {
        setPeriod('Q2 2024');
        setDocumentType('quarterly');
      } else if (filename.includes('q3') || filename.includes('kv3')) {
        setPeriod('Q3 2024');
        setDocumentType('quarterly');
      } else if (filename.includes('q4') || filename.includes('kv4')) {
        setPeriod('Q4 2024');
        setDocumentType('quarterly');
      } else if (filename.includes('2024')) {
        setPeriod('2024');
        setDocumentType('annual');
      }
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'application/vnd.ms-excel': ['.xls'],
      'text/csv': ['.csv']
    },
    multiple: false
  });

  const processExcelFile = async (file: File) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target?.result as ArrayBuffer);
          const workbook = XLSX.read(data, { type: 'array' });
          const sheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[sheetName];
          const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
          
          // Basic validation - check if we have data
          if (jsonData.length < 2) {
            reject(new Error('Excel-filen verkar vara tom eller har fel format'));
            return;
          }
          
          // For now, just resolve with basic info
          // In a real implementation, you would extract specific financial data
          resolve({
            revenue: null,
            ebit: null,
            net_income: null,
            total_assets: null,
            total_liabilities: null,
            equity: null,
            cash_and_equivalents: null,
            current_assets: null,
            current_liabilities: null,
            long_term_debt: null
          });
        } catch (error) {
          reject(new Error('Kunde inte läsa Excel-filen. Kontrollera att filen inte är skadad.'));
        }
      };
      reader.readAsArrayBuffer(file);
    });
  };

  const handleUpload = async () => {
    if (!selectedFile || !period || !documentType) {
      toast.error('Vänligen fyll i alla obligatoriska fält');
      return;
    }

    setUploading(true);

    try {
      // Upload file to Supabase Storage
      const fileName = `${companyId}/${Date.now()}_${selectedFile.name}`;
      const { error: uploadError } = await supabase.storage
        .from(STORAGE_BUCKETS.COMPANY_DOCUMENTS)
        .upload(fileName, selectedFile, {
          cacheControl: '3600',
          contentType: selectedFile.type
        });

      if (uploadError) throw uploadError;

      // Save document record to database
      const { data: documentData, error: documentError } = await supabase
        .from(TABLES.COMPANY_DOCUMENTS)
        .insert([{
          company_id: companyId,
          document_type: documentType,
          period: period,
          file_path: fileName,
          original_filename: selectedFile.name,
          file_size: selectedFile.size,
          processing_status: 'pending'
        }])
        .select()
        .single();

      if (documentError) throw documentError;

      // Process Excel file and extract financial data
      try {
        const financialData = await processExcelFile(selectedFile);
        
        // Save financial data to database
        const { error: financialError } = await supabase
          .from(TABLES.FINANCIAL_DATA)
          .insert([{
            company_id: companyId,
            document_id: documentData.id,
            period: period,
            data_type: documentType,
            ...financialData
          }]);

        if (financialError) {
          console.warn('Could not save financial data:', financialError);
        }

        // Update document status to processed
        await supabase
          .from(TABLES.COMPANY_DOCUMENTS)
          .update({ 
            processing_status: 'processed',
            processed_at: new Date().toISOString()
          })
          .eq('id', documentData.id);

      } catch (processingError) {
        console.error('Error processing file:', processingError);
        
        // Update document status to error
        await supabase
          .from(TABLES.COMPANY_DOCUMENTS)
          .update({ processing_status: 'error' })
          .eq('id', documentData.id);
      }

      toast.success('Dokument uppladdat framgångsrikt');
      
      // Reset form
      setSelectedFile(null);
      setPeriod('');
      setDocumentType('quarterly');
      
      onSuccess?.();

    } catch (error: any) {
      console.error('Error uploading document:', error);
      toast.error('Kunde inte ladda upp dokument: ' + error.message);
    } finally {
      setUploading(false);
    }
  };

  const removeFile = () => {
    setSelectedFile(null);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Upload className="h-5 w-5 mr-2" />
          Ladda upp dokument
        </CardTitle>
        <CardDescription>
          Ladda upp kvartals- eller årsrapporter i Excel-format
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* File Upload Area */}
        {!selectedFile ? (
          <div
            {...getRootProps()}
            className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
              isDragActive 
                ? 'border-blue-400 bg-blue-50' 
                : 'border-gray-300 hover:border-gray-400'
            }`}
          >
            <input {...getInputProps()} />
            <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            {isDragActive ? (
              <p className="text-blue-600">Släpp filen här...</p>
            ) : (
              <div>
                <p className="text-gray-600 mb-2">
                  Dra och släpp en Excel-fil här, eller klicka för att välja
                </p>
                <p className="text-sm text-gray-500">
                  Stödda format: .xlsx, .xls, .csv (max 10MB)
                </p>
              </div>
            )}
          </div>
        ) : (
          <div className="flex items-center justify-between p-4 border rounded-lg bg-gray-50">
            <div className="flex items-center space-x-3">
              <FileText className="h-8 w-8 text-gray-600" />
              <div>
                <p className="font-medium">{selectedFile.name}</p>
                <p className="text-sm text-gray-500">
                  {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                </p>
              </div>
            </div>
            <Button variant="outline" size="sm" onClick={removeFile}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        )}

        {/* Document Details */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="documentType">Dokumenttyp *</Label>
            <Select value={documentType} onValueChange={(value: 'quarterly' | 'annual') => setDocumentType(value)}>
              <SelectTrigger>
                <SelectValue placeholder="Välj dokumenttyp" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="quarterly">Kvartalsrapport</SelectItem>
                <SelectItem value="annual">Årsrapport</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="period">Period *</Label>
            <Input
              id="period"
              value={period}
              onChange={(e) => setPeriod(e.target.value)}
              placeholder={documentType === 'quarterly' ? 'Q1 2024' : '2024'}
            />
          </div>
        </div>

        {/* Upload Button */}
        <Button 
          onClick={handleUpload} 
          disabled={!selectedFile || !period || uploading}
          className="w-full"
        >
          {uploading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Laddar upp...
            </>
          ) : (
            <>
              <Upload className="h-4 w-4 mr-2" />
              Ladda upp dokument
            </>
          )}
        </Button>
      </CardContent>
    </Card>
  );
}
