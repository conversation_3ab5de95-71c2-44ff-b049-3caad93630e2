
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Minimal Design System - Inspired by TerraLabs */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 220 10% 15%;

    --card: 0 0% 100%;
    --card-foreground: 220 10% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 220 10% 15%;

    --primary: 220 10% 15%;
    --primary-foreground: 0 0% 98%;

    --secondary: 220 5% 96%;
    --secondary-foreground: 220 10% 15%;

    --muted: 220 5% 96%;
    --muted-foreground: 220 5% 45%;

    --accent: 220 5% 96%;
    --accent-foreground: 220 10% 15%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 220 10% 15%;

    --radius: 0.5rem;

    /* Minimal Color Palette */
    --neutral-50: 0 0% 98%;
    --neutral-100: 220 5% 96%;
    --neutral-200: 220 5% 90%;
    --neutral-300: 220 5% 80%;
    --neutral-400: 220 5% 60%;
    --neutral-500: 220 5% 45%;
    --neutral-600: 220 10% 30%;
    --neutral-700: 220 10% 20%;
    --neutral-800: 220 10% 15%;
    --neutral-900: 220 15% 10%;
    
    --accent-blue: 210 100% 50%;
    --accent-blue-light: 210 100% 85%;
  }

  .dark {
    --background: 220 15% 10%;
    --foreground: 0 0% 98%;

    --card: 220 15% 10%;
    --card-foreground: 0 0% 98%;

    --popover: 220 15% 10%;
    --popover-foreground: 0 0% 98%;

    --primary: 0 0% 98%;
    --primary-foreground: 220 15% 10%;

    --secondary: 220 10% 15%;
    --secondary-foreground: 0 0% 98%;

    --muted: 220 10% 15%;
    --muted-foreground: 220 5% 65%;

    --accent: 220 10% 15%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 220 10% 15%;
    --input: 220 10% 15%;
    --ring: 0 0% 98%;

    /* Dark Mode Colors */
    --neutral-50: 220 15% 10%;
    --neutral-100: 220 10% 15%;
    --neutral-200: 220 10% 20%;
    --neutral-300: 220 10% 30%;
    --neutral-400: 220 5% 45%;
    --neutral-500: 220 5% 60%;
    --neutral-600: 220 5% 80%;
    --neutral-700: 220 5% 90%;
    --neutral-800: 220 5% 96%;
    --neutral-900: 0 0% 98%;
    
    --accent-blue: 210 100% 60%;
    --accent-blue-light: 210 100% 25%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
  }
}

@layer utilities {
  .text-gradient {
    background: linear-gradient(135deg, hsl(var(--accent-blue)) 0%, hsl(var(--primary)) 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}
